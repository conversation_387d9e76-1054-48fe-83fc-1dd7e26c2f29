return {
    {
        "L3MON4D3/LuaSnip",
        dependencies = { "saadparwaiz1/cmp_luasnip", "rafamadriz/friendly-snippets" },
    },
    {
        "hrsh7th/cmp-nvim-lsp", -- <PERSON> ch<PERSON>h cho autocomplete
        "hrsh7th/cmp-buffer", -- Autocomplete từ buffer
        "hrsh7th/cmp-path", -- Autocomplete path
        "hrsh7th/cmp-cmdline", -- Autocomplete commandline
    },
    {
        "hrsh7th/nvim-cmp", -- Engine chính cho autocomplete
        config = function()
            local cmp = require("cmp")
            require("luasnip.loaders.from_vscode").lazy_load()

            cmp.setup({
                snippet = {
                    expand = function(args)
                        require("luasnip").lsp_expand(args.body)
                    end,
                },
                window = {
                    completion = cmp.config.window.bordered(),
                    documentation = cmp.config.window.bordered(),
                },
                mapping = cmp.mapping.preset.insert({
                    ["<C-b>"] = cmp.mapping.scroll_docs(-4),
                    ["<C-f>"] = cmp.mapping.scroll_docs(4),
                    ["<C-Space>"] = cmp.mapping.complete(),
                    ["<C-e>"] = cmp.mapping.abort(),
                    ["<CR>"] = cmp.mapping.confirm({ select = true }),
                    ["<C-k>"] = cmp.mapping.select_prev_item(),
                    ["<C-j>"] = cmp.mapping.select_next_item(),
                }),
                sources = cmp.config.sources({
                    { name = "nvim_lsp" },
                    { name = "luasnip" },
                    { name = "zls" },
                    { name = "buffer" },
                    { name = "path" },
                    { name = "pylsp" },
                    { name = "gci" },
                    { name = "ts_ls" },
                    { name = "gopls" },
                    { name = "nix" },
                    { name = "buf_ls" },
                    { name = "render-markdown" },
                }),
            })
        end,
    },
}