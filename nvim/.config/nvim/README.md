# dotfiles của killerkidbo

## Cài đặt nhanh

```bash
git clone https://github.com/heyhuynhgiabuu/dotfiles.git ~/dotfiles
cd ~/dotfiles
bash scripts/install.sh
```

## <PERSON><PERSON><PERSON> phím tắt nổi bật

- `<leader>e`: Mở file explorer (Neo-tree)
- `<leader>ff`: Tìm file (Telescope)
- `<leader>fn`: Tạo/mở ghi chú markdown
- `<leader>gs`: Stage git hunk
- `<leader>mp`: Preview markdown

## Plugin nổi bật

- UI: lualine, bufferline, neo-tree, which-key, autopairs, colorizer, indent-blankline, notify, noice, web-devicons
- Productivity: comment, surround, markdown-preview, render-markdown, floating-term, help-floating, snipets, vim-helpers
- LSP: nvim-cmp, luasnip, mason, lspkind, jdtls, treesitter, completions
- Git: gitsigns, fugitive

## Ghi chú

- <PERSON><PERSON> gõ tiếng Vi<PERSON><PERSON> mư<PERSON> m<PERSON>, đã tích hợp im-select.
- <PERSON><PERSON> tối ưu tốc độ, đã lazy-load nhiều plugin theo event/filetype.