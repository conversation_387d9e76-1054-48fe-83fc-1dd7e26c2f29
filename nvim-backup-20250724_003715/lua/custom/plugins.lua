-- Custom plugins for NvChad
local plugins = {
  -- Your existing plugins adapted for NvChad
  {
    "iamcco/markdown-preview.nvim",
    build = "cd app && yarn install",
    ft = { "markdown" },
    config = function()
      vim.g.mkdp_filetypes = { "markdown" }
    end,
  },
  
  -- Add other plugins from your current setup
  {
    "mfussenegger/nvim-jdtls",
    ft = "java",
  },
  
  -- Disable conflicting NvChad plugins if needed
  {
    "nvim-tree/nvim-tree.lua",
    enabled = false, -- if you prefer neo-tree
  },
}

return plugins