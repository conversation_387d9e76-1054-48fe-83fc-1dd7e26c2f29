#!/bin/bash

echo "🛠️ Installing dotfiles..."

# Backup old dotfiles if exist
echo "Backing up old dotfiles..."
mv ~/.zshrc ~/.zshrc.backup 2>/dev/null
ln -sf ~/dotfiles/.zshrc ~/.zshrc

mkdir -p ~/.zsh
ln -sf ~/dotfiles/.zsh/* ~/.zsh/

# Tmux config
echo "Symlinking tmux config..."
ln -sf ~/dotfiles/.tmux.conf ~/.tmux.conf

# Neovim config
echo "Symlinking Neovim config..."
mkdir -p ~/.config/nvim
ln -sf ~/dotfiles/.config/nvim/init.lua ~/.config/nvim/init.lua

# Backup existing Neovim config before NvChad installation
echo "Backing up existing Neovim configuration..."
if [ -d ~/.config/nvim ]; then
    BACKUP_DIR="$HOME/dotfiles/nvim-backup-$(date +%Y%m%d_%H%M%S)"
    cp -r ~/.config/nvim "$BACKUP_DIR"
    echo "Backup created at: $BACKUP_DIR"
fi

# Remove existing nvim symlink/directory
rm -rf ~/.config/nvim

# Install NvChad v2.0
echo "Installing NvChad v2.0..."
git clone --depth 1 -b v2.0 https://github.com/NvChad/NvChad.git ~/.config/nvim

# Create custom directory for your configurations
mkdir -p ~/.config/nvim/lua/custom

# Create bin directory and symlink scripts
echo "Setting up scripts..."
BIN_DIR="$HOME/.bin"
DOTFILES_SCRIPTS_DIR="$HOME/dotfiles/scripts"
mkdir -p "$BIN_DIR"

# Symlink all files from scripts directory to ~/.bin
# and make them executable.
for script in "$DOTFILES_SCRIPTS_DIR"/*; do
    if [ -f "$script" ]; then
        script_name=$(basename "$script")
        echo "Symlinking $script_name..."
        ln -sf "$script" "$BIN_DIR/$script_name"
        chmod +x "$BIN_DIR/$script_name"
    fi
done

echo "✅ Done! Now run: source ~/.zshrc"

