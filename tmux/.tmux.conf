# Sử dụng vi-style
setw -g mode-keys vi

# <PERSON> phép sử dụng chuột
set -g mouse on

# Thời gian chuyển pane (ms)
set -g display-time 1000

# D<PERSON> thấy hơn khi đổi window
set -g status-keys vi
set -g status-style "bg=black,fg=green"
set -g status-left '#[fg=cyan]#S #[fg=white]|'
set -g status-right '#[fg=yellow]%Y-%m-%d #[fg=cyan]%H:%M:%S'
set -g @continuum-restore 'on'

# Số lượng dòng lịch sử
set -g history-limit 10000

# Reload config
bind r source-file ~/.tmux.conf \; display-message "🔁 Reloaded!"

# Di chuyển pane dễ hơn (hướng vi)
bind h select-pane -L
bind j select-pane -D
bind k select-pane -U
bind l select-pane -R

# Open note
bind-key n run-shell "$HOME/dotfiles/scripts/note.sh"

# Quick note binding - từ video Kunkka
bind-key E split-window -h \; send-keys "nvim ~/notes/notes-$(date +%Y%m%d-%H%M%S).md" Enter


# Plugin Manager
set -g @plugin 'tmux-plugins/tpm'

# Plugins đề xuất:
set -g @plugin 'tmux-plugins/tmux-sensible'       # Cài đặt tối ưu mặc định
set -g @plugin 'tmux-plugins/tmux-resurrect'      # Khôi phục session layout
set -g @plugin 'tmux-plugins/tmux-continuum'      # Auto save layout
set -g @plugin 'christoomey/vim-tmux-navigator'   # Dùng h/j/k/l di chuyển vim + tmux

# Khởi động TPM
if "[[ ! -d $HOME/.tmux/plugins/tpm ]]" \
   "run-shell 'git clone https://github.com/tmux-plugins/tpm $HOME/.tmux/plugins/tpm'"
run '~/.tmux/plugins/tpm/tpm'

# Reload config & install plugin nếu chưa có
run-shell 'test ! -d ~/.tmux/plugins/tpm || ~/.tmux/plugins/tpm/bin/install_plugins'
